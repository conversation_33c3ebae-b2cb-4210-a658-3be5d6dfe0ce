<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Action class to retrieve all products using MongoDB raw queries for proper price sorting.
 */
class GetAllProductsRawQuery
{
    /**
     * Handle the product listing request with filters and pagination using raw MongoDB queries.
     *
     * @param array $data The validated request data
     * @return array Contains paginated products, total count, min/max prices
     */
    public function handle(array $data): array
    {
        // Build the aggregation pipeline
        $pipeline = $this->buildAggregationPipeline($data);

        // Execute the aggregation
        $collection = Product::getCollection();
        $results = $collection->aggregate($pipeline)->toArray();

        // Get total count for pagination
        $totalCount = $this->getTotalCount($data);

        // Get min and max prices
        $priceStats = $this->getPriceStatistics();

        // Convert results to Laravel collection and paginate
        $products = collect($results)->map(function ($item) {
            return new Product((array) $item);
        });

        // Apply pagination
        $perPage = $data['per_page'] ?? 15;
        $page = $data['page'] ?? 1;
        $paginatedProducts = $this->paginateCollection($products, $perPage, $page, $totalCount);

        return [
            'products' => $paginatedProducts,
            'total_count' => $totalCount,
            'min_price' => $priceStats['min_price'],
            'max_price' => $priceStats['max_price'],
        ];
    }

    /**
     * Build the MongoDB aggregation pipeline based on filters and sorting.
     *
     * @param array $data
     * @return array
     */
    private function buildAggregationPipeline(array $data): array
    {
        $pipeline = [];

        // Step 1: Match basic filters
        $matchStage = $this->buildMatchStage($data);
        if (!empty($matchStage)) {
            $pipeline[] = ['$match' => $matchStage];
        }

        // Step 2: Lookup variations for price calculation
        $pipeline[] = [
            '$lookup' => [
                'from' => 'variations',
                'localField' => '_id',
                'foreignField' => 'product_id',
                'as' => 'variations'
            ]
        ];

        // Step 3: Add computed fields for effective prices
        $pipeline[] = [
            '$addFields' => [
                'min_effective_price' => [
                    '$min' => [
                        '$map' => [
                            'input' => '$variations',
                            'as' => 'variation',
                            'in' => [
                                '$cond' => [
                                    'if' => [
                                        '$and' => [
                                            ['$ne' => ['$$variation.sale_price', null]],
                                            ['$lt' => ['$$variation.sale_price', '$$variation.price']]
                                        ]
                                    ],
                                    'then' => '$$variation.sale_price',
                                    'else' => '$$variation.price'
                                ]
                            ]
                        ]
                    ]
                ],
                'max_effective_price' => [
                    '$max' => [
                        '$map' => [
                            'input' => '$variations',
                            'as' => 'variation',
                            'in' => [
                                '$cond' => [
                                    'if' => [
                                        '$and' => [
                                            ['$ne' => ['$$variation.sale_price', null]],
                                            ['$lt' => ['$$variation.sale_price', '$$variation.price']]
                                        ]
                                    ],
                                    'then' => '$$variation.sale_price',
                                    'else' => '$$variation.price'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Step 4: Apply price range filtering after calculating effective prices
        $priceMatchStage = $this->buildPriceMatchStage($data);
        if (!empty($priceMatchStage)) {
            $pipeline[] = ['$match' => $priceMatchStage];
        }

        // Step 5: Apply stock filtering
        $stockMatchStage = $this->buildStockMatchStage($data);
        if (!empty($stockMatchStage)) {
            $pipeline[] = ['$match' => $stockMatchStage];
        }

        // Step 6: Apply guarantee filtering
        $guaranteeStage = $this->buildGuaranteeStage($data);
        if (!empty($guaranteeStage)) {
            $pipeline = array_merge($pipeline, $guaranteeStage);
        }

        // Step 7: Apply sorting
        $sortStage = $this->buildSortStage($data);
        if (!empty($sortStage)) {
            $pipeline[] = ['$sort' => $sortStage];
        }

        // Step 8: Apply pagination
        $page = $data['page'] ?? 1;
        $perPage = $data['per_page'] ?? 15;
        $skip = ($page - 1) * $perPage;

        $pipeline[] = ['$skip' => $skip];
        $pipeline[] = ['$limit' => $perPage];

        // Step 9: Lookup related data for final output
        $pipeline[] = [
            '$lookup' => [
                'from' => 'galleries',
                'localField' => '_id',
                'foreignField' => 'imageable_id',
                'as' => 'gallery'
            ]
        ];

        $pipeline[] = [
            '$lookup' => [
                'from' => 'guarantees',
                'localField' => '_id',
                'foreignField' => 'product_id',
                'as' => 'guarantees'
            ]
        ];

        return $pipeline;
    }

    /**
     * Build the initial match stage for basic filters.
     *
     * @param array $data
     * @return array
     */
    private function buildMatchStage(array $data): array
    {
        $match = [];

        // Text search filter
        if (!empty($data['search'])) {
            $searchTerm = trim($data['search']);
            $escapedSearch = str_replace('"', '\"', $searchTerm);
            $match['$text'] = ['$search' => $escapedSearch];
        }

        // Shop filter
        if (!empty($data['shop_id'])) {
            $match['shop_id'] = $data['shop_id'];
        }

        return $match;
    }

    /**
     * Build price range match stage (applied after effective price calculation).
     *
     * @param array $data
     * @return array
     */
    private function buildPriceMatchStage(array $data): array
    {
        $match = [];

        if (isset($data['min_price'])) {
            $match['min_effective_price'] = ['$gte' => (int) $data['min_price']];
        }

        if (isset($data['max_price'])) {
            if (isset($match['min_effective_price'])) {
                $match['min_effective_price']['$lte'] = (int) $data['max_price'];
            } else {
                $match['min_effective_price'] = ['$lte' => (int) $data['max_price']];
            }
        }

        return $match;
    }

    /**
     * Build stock availability match stage.
     *
     * @param array $data
     * @return array
     */
    private function buildStockMatchStage(array $data): array
    {
        if (empty($data['in_stock_only']) || $data['in_stock_only'] !== 'true') {
            return [];
        }

        // Filter products that have variations with positive stock
        return [
            'variations' => [
                '$elemMatch' => [
                    '$expr' => [
                        '$gt' => [
                            ['$sum' => '$purchases.quantity'],
                            0
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Build guarantee filtering stages.
     *
     * @param array $data
     * @return array
     */
    private function buildGuaranteeStage(array $data): array
    {
        if (empty($data['has_guarantee_only']) || $data['has_guarantee_only'] !== 'true') {
            return [];
        }

        return [
            [
                '$lookup' => [
                    'from' => 'guarantees',
                    'localField' => '_id',
                    'foreignField' => 'product_id',
                    'as' => 'product_guarantees'
                ]
            ],
            [
                '$match' => [
                    'product_guarantees' => ['$ne' => []]
                ]
            ]
        ];
    }

    /**
     * Build sort stage based on sort parameter.
     *
     * @param array $data
     * @return array
     */
    private function buildSortStage(array $data): array
    {
        $sort = $data['sort'] ?? 'newest';

        // If text search is active, sort by relevance first
        if (!empty($data['search'])) {
            $sortStage = ['score' => ['$meta' => 'textScore']];

            // Add secondary sort
            switch ($sort) {
                case 'newest':
                    $sortStage['created_at'] = -1;
                    break;
                case 'cheapest':
                    $sortStage['min_effective_price'] = 1;
                    $sortStage['created_at'] = -1;
                    break;
                case 'most_expensive':
                    $sortStage['max_effective_price'] = -1;
                    $sortStage['created_at'] = -1;
                    break;
                default:
                    $sortStage['created_at'] = -1;
            }

            return $sortStage;
        }

        // Normal sorting without text search
        switch ($sort) {
            case 'newest':
                return ['created_at' => -1];
            case 'cheapest':
                return ['min_effective_price' => 1, 'created_at' => -1];
            case 'most_expensive':
                return ['max_effective_price' => -1, 'created_at' => -1];
            case 'most_sales':
            case 'most_popular':
                // TODO: Implement proper sales/popularity metrics
                return ['created_at' => -1];
            case 'most_viewed':
                // TODO: Implement view tracking
                return ['created_at' => -1];
            default:
                return ['created_at' => -1];
        }
    }

    /**
     * Get total count of products matching the filters (without pagination).
     *
     * @param array $data
     * @return int
     */
    private function getTotalCount(array $data): int
    {
        // Build a simplified pipeline for counting
        $pipeline = [];

        // Step 1: Match basic filters
        $matchStage = $this->buildMatchStage($data);
        if (!empty($matchStage)) {
            $pipeline[] = ['$match' => $matchStage];
        }

        // Step 2: Lookup variations for price calculation
        $pipeline[] = [
            '$lookup' => [
                'from' => 'variations',
                'localField' => '_id',
                'foreignField' => 'product_id',
                'as' => 'variations'
            ]
        ];

        // Step 3: Add computed fields for effective prices
        $pipeline[] = [
            '$addFields' => [
                'min_effective_price' => [
                    '$min' => [
                        '$map' => [
                            'input' => '$variations',
                            'as' => 'variation',
                            'in' => [
                                '$cond' => [
                                    'if' => [
                                        '$and' => [
                                            ['$ne' => ['$$variation.sale_price', null]],
                                            ['$lt' => ['$$variation.sale_price', '$$variation.price']]
                                        ]
                                    ],
                                    'then' => '$$variation.sale_price',
                                    'else' => '$$variation.price'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Step 4: Apply price range filtering
        $priceMatchStage = $this->buildPriceMatchStage($data);
        if (!empty($priceMatchStage)) {
            $pipeline[] = ['$match' => $priceMatchStage];
        }

        // Step 5: Apply stock filtering
        $stockMatchStage = $this->buildStockMatchStage($data);
        if (!empty($stockMatchStage)) {
            $pipeline[] = ['$match' => $stockMatchStage];
        }

        // Step 6: Apply guarantee filtering
        $guaranteeStage = $this->buildGuaranteeStage($data);
        if (!empty($guaranteeStage)) {
            $pipeline = array_merge($pipeline, $guaranteeStage);
        }

        // Step 7: Count the results
        $pipeline[] = ['$count' => 'total'];

        $collection = Product::getCollection();
        $result = $collection->aggregate($pipeline)->toArray();

        return $result[0]->total ?? 0;
    }

    /**
     * Get minimum and maximum prices from all product variations.
     *
     * @return array
     */
    private function getPriceStatistics(): array
    {
        $pipeline = [
            [
                '$lookup' => [
                    'from' => 'variations',
                    'localField' => '_id',
                    'foreignField' => 'product_id',
                    'as' => 'variations'
                ]
            ],
            [
                '$unwind' => '$variations'
            ],
            [
                '$addFields' => [
                    'effective_price' => [
                        '$cond' => [
                            'if' => [
                                '$and' => [
                                    ['$ne' => ['$variations.sale_price', null]],
                                    ['$lt' => ['$variations.sale_price', '$variations.price']]
                                ]
                            ],
                            'then' => '$variations.sale_price',
                            'else' => '$variations.price'
                        ]
                    ]
                ]
            ],
            [
                '$group' => [
                    '_id' => null,
                    'min_price' => ['$min' => '$effective_price'],
                    'max_price' => ['$max' => '$effective_price']
                ]
            ]
        ];

        $collection = Product::getCollection();
        $result = $collection->aggregate($pipeline)->toArray();

        if (empty($result)) {
            return ['min_price' => 0, 'max_price' => 0];
        }

        return [
            'min_price' => (int) ($result[0]->min_price ?? 0),
            'max_price' => (int) ($result[0]->max_price ?? 0),
        ];
    }

    /**
     * Manually paginate a collection.
     *
     * @param \Illuminate\Support\Collection $collection
     * @param int $perPage
     * @param int $page
     * @param int $total
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    private function paginateCollection($collection, int $perPage, int $page, int $total)
    {
        return new LengthAwarePaginator(
            $collection,
            $total,
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }
}
