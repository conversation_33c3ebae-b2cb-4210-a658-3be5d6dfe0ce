<?php

namespace App\Services\Actions\Product;

use App\Models\Product\Product;
use App\Models\Product\ProductVariation;
use Illuminate\Database\Eloquent\Builder;

/**
 * Action class to retrieve all products with filtering, sorting, and pagination.
 */
class GetAllProducts
{
    private bool $hasTextSearch = false;

    /**
     * Handle the product listing request with filters and pagination.
     *
     * @param array $data The validated request data
     * @return array Contains paginated products, total count, min/max prices
     */
    public function handle(array $data): array
    {
        // Start with a clean base query
        $query = $this->buildBaseQuery();

        // Apply filters incrementally
        $this->applySearchFilter($query, $data);
        $this->applyPriceRangeFilter($query, $data);
        $this->applyStockFilter($query, $data);
        $this->applyGuaranteeFilter($query, $data);
        $this->applyShopFilter($query, $data);

        // Store sort parameter for later use
        $sortParam = $data['sort'] ?? 'newest';

        // Apply sorting
        $this->applySorting($query, $sortParam);

        // Get total count before pagination
        $totalCount = $query->count();

        // Get min and max prices (optimized calculation)
        $priceStats = $this->getPriceStatistics();

        // For price sorting without text search, we need to handle it differently
        if (!$this->hasTextSearch && in_array($sortParam, ['cheapest', 'most_expensive'])) {
            // Get all products first, then sort by price in PHP
            $allProducts = $query->get();
            $sortedProducts = $this->sortProductsByPrice($allProducts, $sortParam);

            // Apply pagination manually
            $perPage = $data['per_page'] ?? 15;
            $page = $data['page'] ?? 1;
            $products = $this->paginateCollection($sortedProducts, $perPage, $page);
        } else {
            // Apply pagination normally
            $perPage = $data['per_page'] ?? 15;
            $page = $data['page'] ?? 1;
            $products = $query->paginate($perPage, ['*'], 'page', $page);
        }

        return [
            'products' => $products,
            'total_count' => $totalCount,
            'min_price' => $priceStats['min_price'],
            'max_price' => $priceStats['max_price'],
        ];
    }

    /**
     * Build the base query with essential relationships.
     *
     * @return Builder
     */
    private function buildBaseQuery(): Builder
    {
        return Product::with(['variations', 'gallery', 'guarantees']);
    }

    /**
     * Apply search filter with text search and relevance scoring.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applySearchFilter(Builder $query, array $data): void
    {
        if (!empty($data['search'])) {
            $this->hasTextSearch = true;

            // Use MongoDB text search with proper escaping
            $searchTerm = trim($data['search']);

            // For MongoDB text search, we need to be careful with special characters
            // Escape quotes and handle phrase searches
            $escapedSearch = str_replace('"', '\"', $searchTerm);

            $query->where('$text', ['$search' => $escapedSearch]);

            // Project the text score for relevance sorting
            $query->project([
                '*',
                'score' => ['$meta' => 'textScore']
            ]);
        }
    }

    /**
     * Apply price range filter considering both regular and sale prices (MongoDB-compatible).
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyPriceRangeFilter(Builder $query, array $data): void
    {
        if (!isset($data['min_price']) && !isset($data['max_price'])) {
            return;
        }

        // MongoDB-compatible approach: Use separate conditions for sale_price and regular price
        $query->whereHas('variations', function ($variationQuery) use ($data) {
            if (isset($data['min_price'])) {
                $minPrice = (int) $data['min_price'];

                $variationQuery->where(function ($priceQuery) use ($minPrice) {
                    // Check sale_price if it exists and meets minimum
                    $priceQuery->where(function ($saleQuery) use ($minPrice) {
                        $saleQuery->whereNotNull('sale_price')
                            ->where('sale_price', '>=', $minPrice);
                    })
                        // OR check regular price only when sale_price is null
                        ->orWhere(function ($regularQuery) use ($minPrice) {
                            $regularQuery->whereNull('sale_price')
                                ->where('price', '>=', $minPrice);
                        });
                });
            }

            if (isset($data['max_price'])) {
                $maxPrice = (int) $data['max_price'];

                $variationQuery->where(function ($priceQuery) use ($maxPrice) {
                    // Check sale_price if it exists and is within range
                    $priceQuery->where(function ($saleQuery) use ($maxPrice) {
                        $saleQuery->whereNotNull('sale_price')
                            ->where('sale_price', '<=', $maxPrice);
                    })
                        // OR check regular price only when sale_price is null
                        ->orWhere(function ($regularQuery) use ($maxPrice) {
                            $regularQuery->whereNull('sale_price')
                                ->where('price', '<=', $maxPrice);
                        });
                });
            }
        });
    }

    /**
     * Apply stock availability filter using the ledger-based inventory system.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyStockFilter(Builder $query, array $data): void
    {
        if (empty($data['in_stock_only']) || $data['in_stock_only'] !== 'true') {
            return;
        }

        $query->whereHas('variations', function ($variationQuery) {
            // Check for variations that have positive stock based on purchase entries
            // This works with the ledger-based inventory system where:
            // - Positive quantities are purchases/restocks
            // - Negative quantities are sales
            $variationQuery->whereHas('purchases', function ($purchaseQuery) {
                // Check if there are any purchase entries with positive quantity
                $purchaseQuery->where('quantity', '>', 0);
            });
        });
    }

    /**
     * Apply guarantee availability filter.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyGuaranteeFilter(Builder $query, array $data): void
    {
        if (empty($data['has_guarantee_only']) || $data['has_guarantee_only'] !== 'true') {
            return;
        }

        $query->whereHas('guarantees');
    }

    /**
     * Apply shop filter for filtering products by shop.
     *
     * @param Builder $query
     * @param array $data
     * @return void
     */
    private function applyShopFilter(Builder $query, array $data): void
    {
        if (empty($data['shop_id'])) {
            return;
        }

        $query->where('shop_id', $data['shop_id']);
    }

    /**
     * Apply sorting to the query based on the sort parameter.
     *
     * @param Builder $query
     * @param string $sort
     * @return void
     */
    private function applySorting(Builder $query, string $sort): void
    {
        // When text search is active, relevance should be the primary sort
        if ($this->hasTextSearch) {
            $query->orderBy('score', 'desc');

            // Add secondary sort based on the requested sort parameter
            switch ($sort) {
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'cheapest':
                    // For text search with price sorting, we need to use a different approach
                    // Since MongoDB text search with complex aggregation is limited,
                    // we'll sort by created_at as secondary but add a note for future optimization
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'most_expensive':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'most_sales':
                case 'most_popular':
                    // Use comment count as secondary sort
                    $query->withCount('comments')->orderBy('comments_count', 'desc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
            return;
        }

        // Normal sorting when no text search is active - use proper price sorting
        switch ($sort) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'cheapest':
                $this->applyCheapestSort($query);
                break;
            case 'most_expensive':
                $this->applyMostExpensiveSort($query);
                break;
            case 'most_sales':
                $this->applyMostSalesSort($query);
                break;
            case 'most_viewed':
                // TODO: Implement view tracking and sorting
                $query->orderBy('created_at', 'desc');
                break;
            case 'most_popular':
                $this->applyMostPopularSort($query);
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }
    }

    /**
     * Apply cheapest price sorting using a hybrid approach for MongoDB compatibility.
     *
     * @param Builder $query
     * @return void
     */
    private function applyCheapestSort(Builder $query): void
    {
        // For MongoDB, we'll use a hybrid approach:
        // 1. Get products with their variations loaded
        // 2. Calculate effective prices in PHP
        // 3. Sort the collection

        // Load variations relationship for price calculation
        $query->with(['variations' => function ($variationQuery) {
            $variationQuery->select('product_id', 'price', 'sale_price');
        }]);

        // We'll handle the sorting after the query is executed
        // This is done in the handle method by modifying the collection
    }

    /**
     * Apply most expensive price sorting using a hybrid approach for MongoDB compatibility.
     *
     * @param Builder $query
     * @return void
     */
    private function applyMostExpensiveSort(Builder $query): void
    {
        // For MongoDB, we'll use a hybrid approach:
        // 1. Get products with their variations loaded
        // 2. Calculate effective prices in PHP
        // 3. Sort the collection

        // Load variations relationship for price calculation
        $query->with(['variations' => function ($variationQuery) {
            $variationQuery->select('product_id', 'price', 'sale_price');
        }]);

        // We'll handle the sorting after the query is executed
        // This is done in the handle method by modifying the collection
    }

    /**
     * Apply most sales sorting using invoice products as proxy.
     *
     * @param Builder $query
     * @return void
     */
    private function applyMostSalesSort(Builder $query): void
    {
        // Use comment count as a proxy for sales popularity
        // TODO: Implement proper sales tracking using InvoiceProduct model
        $query->withCount('comments')->orderBy('comments_count', 'desc');
    }

    /**
     * Apply most popular sorting using multiple metrics.
     *
     * @param Builder $query
     * @return void
     */
    private function applyMostPopularSort(Builder $query): void
    {
        // Use comment count as popularity metric
        // TODO: Combine with view count, sales count, and rating when available
        $query->withCount('comments')->orderBy('comments_count', 'desc');
    }

    /**
     * Get minimum and maximum prices from all product variations (MongoDB-compatible).
     *
     * @return array
     */
    private function getPriceStatistics(): array
    {
        // Use the fallback method which is reliable and works well with MongoDB
        // This approach loads data into PHP memory but is guaranteed to work correctly
        return $this->getPriceStatisticsFallback();
    }

    /**
     * Fallback method for price statistics calculation.
     *
     * @return array
     */
    private function getPriceStatisticsFallback(): array
    {
        $variations = ProductVariation::all(['price', 'sale_price']);

        if ($variations->isEmpty()) {
            return ['min_price' => 0, 'max_price' => 0];
        }

        $prices = $variations->map(function ($variation) {
            $salePrice = $variation->sale_price;
            $regularPrice = $variation->price;
            return ($salePrice && $salePrice < $regularPrice) ? $salePrice : $regularPrice;
        })->filter()->values();

        if ($prices->isEmpty()) {
            return ['min_price' => 0, 'max_price' => 0];
        }

        return [
            'min_price' => (int) $prices->min(),
            'max_price' => (int) $prices->max(),
        ];
    }

    /**
     * Sort products by their effective price (considering sale_price vs regular price).
     *
     * @param \Illuminate\Database\Eloquent\Collection $products
     * @param string $sortDirection
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function sortProductsByPrice($products, string $sortDirection)
    {
        return $products->sort(function ($productA, $productB) use ($sortDirection) {
            $priceA = $this->getProductMinEffectivePrice($productA);
            $priceB = $this->getProductMinEffectivePrice($productB);

            if ($sortDirection === 'cheapest') {
                return $priceA <=> $priceB;
            } else { // most_expensive
                return $priceB <=> $priceA;
            }
        })->values(); // Reset array keys
    }

    /**
     * Get the minimum effective price for a product from its variations.
     *
     * @param \App\Models\Product\Product $product
     * @return float
     */
    private function getProductMinEffectivePrice($product): float
    {
        if (!$product->relationLoaded('variations') || $product->variations->isEmpty()) {
            return 0;
        }

        $minPrice = $product->variations->map(function ($variation) {
            $salePrice = $variation->sale_price;
            $regularPrice = $variation->price;

            // Use sale_price if it exists and is lower than regular price
            return ($salePrice && $salePrice < $regularPrice) ? $salePrice : $regularPrice;
        })->filter()->min();

        return $minPrice ?? 0;
    }

    /**
     * Manually paginate a collection.
     *
     * @param \Illuminate\Database\Eloquent\Collection $collection
     * @param int $perPage
     * @param int $page
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    private function paginateCollection($collection, int $perPage, int $page)
    {
        $total = $collection->count();
        $offset = ($page - 1) * $perPage;
        $items = $collection->slice($offset, $perPage)->values();

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }
}
