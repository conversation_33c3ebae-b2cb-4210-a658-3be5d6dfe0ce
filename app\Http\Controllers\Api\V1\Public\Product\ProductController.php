<?php

namespace App\Http\Controllers\Api\V1\Public\Product;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Product\GetAllProductsRequest;
use App\Http\Resources\Product\ProductIndexResource;
use App\Services\Actions\Product\GetAllProducts;
use App\Services\Actions\Product\GetAllProductsRawQuery;
use App\Services\Actions\Product\GetProductBySlug;
use App\Http\Resources\Product\ProductResource;
use App\Http\Requests\Product\GetProductBySlugRequest;

/**
 * Controller for managing and retrieving product information
 *
 * @group Product Management
 */
class ProductController extends BaseController
{
    /**
     * Display the specified product by slug
     *
     * @param GetProductBySlugRequest $request The validated request
     * @param string $slug The product slug
     * @param GetProductBySlug $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function show(GetProductBySlugRequest $request, string $slug, GetProductBySlug $action)
    {
        // The $slug parameter is used by the route but the actual value is handled in the request
        $product = $action->handle($request->validated());

        return $this->sendResponse(
            new ProductResource($product),
            __('messages.product.found')
        );
    }


    /**
     * Display a listing of all products with filtering and pagination
     *
     * @param GetAllProductsRequest $request The validated request with filters
     * @param GetAllProducts $action The action service
     * @return \Illuminate\Http\JsonResponse
     *
     * @unauthenticated
     */
    public function index(GetAllProductsRequest $request, GetAllProductsRawQuery $action)
    {
        $products = $action->handle($request->validated());

        return $this->sendResponse(
            new ProductIndexResource($products),
            __('messages.product.found')
        );
    }
}
