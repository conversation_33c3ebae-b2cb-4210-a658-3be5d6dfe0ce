<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

/**
 * Migration to add text index to products collection for full-text search.
 */
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create text index on products collection
        // This enables MongoDB $text search on title, description, and meta fields
        DB::connection('mongodb')->collection('products')->raw(function ($collection) {
            $collection->createIndex([
                'title' => 'text',
                'description' => 'text',
                'meta_title' => 'text',
                'meta_description' => 'text'
            ], [
                'name' => 'products_text_index',
                'default_language' => 'none', // Disable language-specific stemming for Persian/multilingual content
                'weights' => [
                    'title' => 10,           // Highest weight for title matches
                    'meta_title' => 8,       // High weight for meta title
                    'description' => 5,      // Medium weight for description
                    'meta_description' => 3  // Lower weight for meta description
                ]
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the text index
        DB::connection('mongodb')->collection('products')->raw(function ($collection) {
            $collection->dropIndex('products_text_index');
        });
    }
};
